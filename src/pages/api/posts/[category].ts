import type { APIRoute } from 'astro'
import { getCollection } from 'astro:content'
import { unsluglify } from '@/utils'

export const prerender = false

export const GET: APIRoute = async ({ params, url }) => {
  const { category } = params
  const searchParams = url.searchParams
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '6')
  
  if (!category) {
    return new Response(JSON.stringify({ error: 'Category is required' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    })
  }

  try {
    const posts = await getCollection('blog')
    const unsluglifyNameCategory = unsluglify(category.toLowerCase())
    
    // Filter posts by category and exclude drafts
    const filteredPosts = posts
      .filter((post) => !post.data.draft)
      .filter((post) => post.data.category.toLowerCase() === unsluglifyNameCategory)
      .sort((a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf())

    // Calculate pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex)
    
    // Transform posts to include only necessary data
    const transformedPosts = await Promise.all(
      paginatedPosts.map(async (post) => {
        const { remarkPluginFrontmatter } = await post.render()
        return {
          id: post.id,
          slug: post.slug,
          data: {
            title: post.data.title,
            description: post.data.description,
            pubDate: post.data.pubDate.toISOString(),
            heroImage: post.data.heroImage,
            category: post.data.category,
            tags: post.data.tags,
            draft: post.data.draft
          },
          readTime: remarkPluginFrontmatter.minutesRead
        }
      })
    )

    const hasMore = endIndex < filteredPosts.length
    const totalPosts = filteredPosts.length
    const totalPages = Math.ceil(totalPosts / limit)

    return new Response(JSON.stringify({
      posts: transformedPosts,
      pagination: {
        currentPage: page,
        totalPages,
        totalPosts,
        hasMore,
        limit
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Error fetching posts:', error)
    return new Response(JSON.stringify({ error: 'Failed to fetch posts' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
