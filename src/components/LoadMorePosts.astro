---
import type { CollectionEntry } from 'astro:content'
import PostCard from '@/components/PostCard'
import { cn } from '@/utils'

type Props = {
	initialPosts: CollectionEntry<'blog'>[]
	FirstBig?: boolean
}

const { initialPosts, FirstBig = false } = Astro.props

// Render initial posts with reading time
const initialPostsWithReadTime = await Promise.all(
	initialPosts.map(async (post) => {
		const { remarkPluginFrontmatter } = await post.render()
		return {
			id: post.id,
			slug: post.slug,
			data: post.data,
			readTime: remarkPluginFrontmatter.minutesRead
		}
	})
)
---

<div id="posts-container">
	<section
		id="posts-grid"
		class={cn(
			`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 mt-3`,
			FirstBig && `md:[&>*:first-child]:col-span-2`
		)}
	>
		{
			initialPostsWithReadTime.map((post) => (
				<PostCard
					id={post.id}
					data={post.data}
					slug={post.slug}
					readTime={post.readTime}
				/>
			))
		}
	</section>

	<div class="flex justify-center mt-8">
		<button
			id="load-more-btn"
			class="flex items-center justify-center px-8 md:px-4 h-10 text-base font-medium text-black bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-transparent dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
		>
			<span id="load-more-text">Load More</span>
			<span id="loading-spinner" class="hidden ml-2">
				<svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
					<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
					<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
				</svg>
			</span>
		</button>
	</div>
</div>

<script>
	let currentPage = 1;
	let isLoading = false;
	let hasMore = true;

	const loadMoreBtn = document.getElementById('load-more-btn') as HTMLButtonElement;
	const loadMoreText = document.getElementById('load-more-text') as HTMLSpanElement;
	const loadingSpinner = document.getElementById('loading-spinner') as HTMLSpanElement;
	const postsGrid = document.getElementById('posts-grid') as HTMLElement;

	// Function to create a post card element
	function createPostCard(post: any): string {
		const formattedDate = new Date(post.data.pubDate).toLocaleDateString('en-us', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});

		return `
			<article class="grid grid-rows-[300px_auto] md:grid-rows-[300px_220px] min-h-full group">
				<a class="relative overflow-hidden" href="/post/${post.slug}/">
					<img
						src="${post.data.heroImage}"
						width="600"
						height="200"
						class="h-full min-w-full object-cover hover:scale-[101%] transition-all duration-200 rounded-[2px]"
						alt="img of ${post.data.title}"
					/>
					<div class="z-30 absolute bottom-0 w-full h-20">
						<div class="-z-10 absolute bottom-0 glass w-full min-h-full"></div>
						<div class="flex items-center justify-between gap-x-1 text-white px-6 py-4">
							<div class="flex flex-col gap-1 items-center justify-center">
								<time class="text-sm font-bold text-opacity-60" datetime="${post.data.pubDate}">
									${formattedDate}
								</time>
								<span class="text-sm">${post.readTime}</span>
							</div>
							<span class="pb-4">${post.data.category}</span>
						</div>
					</div>
				</a>
				<div class="flex justify-between flex-col gap-4 md:gap-0 py-6 pl-1">
					<div class="flex flex-col gap-3">
						<div class="flex flex-col gap-2">
							<a class="text-2xl font-semibold -tracking-wider" href="/post/${post.slug}/">
								${post.data.title}
							</a>
						</div>
						<p class="overflow-hidden line-clamp-3 text-gray-700 dark:text-white mb-5 font-[400] md:pr-[15%]">
							${post.data.description}
						</p>
					</div>
					<footer class="flex justify-between items-center">
						<a
							href="/post/${post.slug}/"
							class="flex justify-center items-center dark:text-white rounded-full hover:translate-x-1 transition-transform duration-150 ease-in-out font-semibold gap-1 group"
							aria-label="go to ${post.data.title}"
						>
							Read Post <span class="mt-[1px] group-hover:rotate-45 transition-transform duration-250 ease-in-out">
								<svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
							</span>
						</a>
					</footer>
				</div>
			</article>
		`;
	}

	// Load more posts function
	async function loadMorePosts() {
		if (isLoading || !hasMore) return;

		isLoading = true;
		loadMoreBtn.disabled = true;
		loadMoreText.textContent = 'Loading...';
		loadingSpinner.classList.remove('hidden');

		try {
			console.log(`Fetching page ${currentPage + 1}`);
			const response = await fetch(`/api/posts?page=${currentPage + 1}&limit=5`);
			const data = await response.json();
			console.log('API Response:', data);

			if (data.posts && data.posts.length > 0) {
				// Add new posts to the grid
				data.posts.forEach((post: any) => {
					const postElement = document.createElement('div');
					postElement.innerHTML = createPostCard(post);
					postsGrid.appendChild(postElement.firstElementChild!);
				});

				currentPage++;
				hasMore = data.hasMore;

				if (!hasMore) {
					loadMoreBtn.style.display = 'none';
				}
			} else {
				hasMore = false;
				loadMoreBtn.style.display = 'none';
			}
		} catch (error) {
			console.error('Error loading more posts:', error);
			loadMoreText.textContent = 'Error loading posts';
		} finally {
			isLoading = false;
			loadMoreBtn.disabled = false;
			loadMoreText.textContent = 'Load More';
			loadingSpinner.classList.add('hidden');
		}
	}

	// Add event listener
	loadMoreBtn?.addEventListener('click', loadMorePosts);
</script>